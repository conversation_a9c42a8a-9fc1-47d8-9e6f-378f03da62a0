import React, { useState, useEffect } from 'react';
import { useSubscription, useStompClient } from 'react-stomp-hooks';
import PropTypes from 'prop-types';
import Vehicle from './Vehicle';
import CustomVehicle from './CustomVehicle';
import Deployment from './Deployment';
import Case from './Case';
import CaseHalf from './CaseHalf';
import CaseQuarter from './CaseQuarter';
import IncomingCallA from './IncomingCallA';
import IncomingCallB from './IncomingCallB';
import Weather from './Weather';
import TotalFrequency from './TotalFrequency';
import Alarm from './Alarm';
import Attendance from './Attendance';
import DoctorOnDuty from './DoctorOnDuty';
import Schedule from './Schedule';
import HandOver from './Handover';
import DigitalRadio from './DigitalRadio';
import AmbulanceRate from './AmbulanceRate';
import Now from './Now';
import NoContent from './NoContent';
import { sendResultMsg, getWsEndpoint } from '../utils/Util.js';

const propTypes = {
  displayNo: PropTypes.number,
  splitNo: PropTypes.number,
  detailSplitNo: PropTypes.number,
};

/**
 * 実際の表示Contentの箱。SourceNoによって、それぞれのContentを表示
 * Source No仕様:<br>
    1 車両コンテンツ情報更新<br>
    2 配備状況コンテンツ情報更新<br>
    3 事案コンテンツ情報更新<br>
    4 簡易事案コンテンツ(1-2サイズ)情報更新<br>
    5 簡易事案コンテンツ(1-4サイズ)情報更新<br>
    6 時刻コンテンツ<br>
    7 着信状況コンテンツA情報更新<br>
    8 着信状況コンテンツB情報更新<br>
    9 気象コンテンツ情報更新<br>
    10 総合度数コンテンツ情報更新<br>
    11 予警報コンテンツ情報更新<br>
    12 出退コンテンツ情報更新<br>
    13 当番医コンテンツ情報更新<br>
    14 予定コンテンツ情報更新<br>
    15 引継事項コンテンツ情報更新<br>
    16 デジタル無線コンテンツ情報更新<br>
    17 救急車稼働率コンテンツ情報更新<br>
    
 * @module SplitScreen
 * @component 
 * @param {*} props
 * @return {*} SplitScreen
 */
const SplitScreen = (props) => {
  /**
 * Server側のIF仕様
 * public class ContentInfo {
      private Integer sourceNo;
      private Integer sourceSplitNo;
      private Integer detailSplitNo; // 面分割番号
      private Object content; //実際のContent内容
    }
 */
  const [contentInfo, setContentInfo] = useState();

  const receiveContent = (message) => {
    console.info('receiveContent: ' + message.body);

    let command = JSON.parse(message.body);
    const isString = (val) => typeof val === 'string';
    if (command.sourceData) {
      if (isString(command.sourceData)) {
        command.sourceData = JSON.parse(command.sourceData);
      }
    }

    if (command) {
      switch (command.detailSplitNo) {
        case 1:
        case 3:
        case 9:
        case 11:
          if (props.detailSplitNo === 1) {
            setContentInfo((split) => ({
              ...split,
              ...command,
            }));
          }
          break;
        case 4:
        case 6:
        case 12:
        case 14:
          if (props.detailSplitNo === 2) {
            setContentInfo((split) => ({
              ...split,
              ...command,
            }));
          }
          break;
        case 5:
        case 7:
        case 13:
        case 15:
          if (props.detailSplitNo === 3) {
            setContentInfo((split) => ({
              ...split,
              ...command,
            }));
          }
          break;
        case 0:
        case 2:
        case 8:
        case 10:
          if (props.detailSplitNo === 0) {
            setContentInfo((split) => ({
              ...split,
              ...command,
            }));
          }
          break;
        default:
          break;
      }
    }
  };

  const wsEndpoint = getWsEndpoint(
    props.displayNo,
    props.splitNo,
    props.detailSplitNo
  );

  useSubscription(wsEndpoint + '/setContent', receiveContent); // Url:  /monitor/0_1_2/setContent

  const stompClient = useStompClient();
  useEffect(() => {
    if (contentInfo?.id) {
      sendResultMsg(stompClient, contentInfo.id, 0);
    }
  }, [contentInfo?.id, stompClient]);

  if (contentInfo && contentInfo?.id) {
    console.log(`TaskID: ${contentInfo?.id}`);
  }

  if (contentInfo && contentInfo.sourceNo >= 0) {
    const sourceData = contentInfo.sourceData;
    sourceData.barTitle = contentInfo.sourceName;
    sourceData.sourceDispPattern = contentInfo.sourceDispPattern;
    switch (contentInfo.sourceNo) {
      case 1:
        return   sourceData.sourceDispPattern == 1 ?  <CustomVehicle {...sourceData} /> : <Vehicle {...sourceData} />;
      case 2:
        return <Deployment {...sourceData} />;
      case 3:
        return <Case {...sourceData} />;
      case 4:
        return <CaseHalf {...sourceData} />;
      case 5:
        return <CaseQuarter {...sourceData} />;
      case 6:
        return <Now {...sourceData} />;
      case 7:
        return <IncomingCallA {...sourceData} />;
      case 8:
        return <IncomingCallB {...sourceData} />;
      case 9:
        return <Weather {...sourceData} />;
      case 10:
        return <TotalFrequency {...sourceData} />;
      case 11:
        return <Alarm {...sourceData} />;
      case 12:
        return <Attendance {...sourceData} />;
      case 13:
        return <DoctorOnDuty {...sourceData} />;
      case 14:
        return <Schedule {...sourceData} />;
      case 15:
        return <HandOver {...sourceData} />;
      case 16:
        return <DigitalRadio {...sourceData} />;
      case 17:
        return <AmbulanceRate {...sourceData} />;
      default:
        return <NoContent />;
    }
  }

  return <NoContent />;
};

SplitScreen.propTypes = propTypes;
export default SplitScreen;
