package com.contents.model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class CustomVehicleContent {

	public CustomVehicleContent() {

	}

	public void bind(VehicleContent content) {

		this.isDeployment = content.getIsDeployment();

		this.items = new ArrayList<CustomVehicleContentEntity>();

		List<VehicleContentTitleName> titleName = content.getTitleName();
		
		if (titleName == null)
			return;
		
		List<CustomVehicleContentEntity> records = new ArrayList<CustomVehicleContentEntity>();

		for (Iterator<VehicleContentTitleName> ite = titleName.iterator(); ite.hasNext();) {

			VehicleContentTitleName vehicleContentTitleName = ite.next();

			if (vehicleContentTitleName.getDisplayText() != null) {

				CustomVehicleContentTitleEntity title = new CustomVehicleContentTitleEntity();
				String displayText = vehicleContentTitleName.getDisplayText();
				String textColor = vehicleContentTitleName.getTextColor();
				String backgroundColor = vehicleContentTitleName.getBackgroundColor();
				DisplayItem item = new DisplayItem(displayText, textColor, backgroundColor);
				title.setTitle(item);
				records.add(title);
			}

			List<VehicleContentDeployment> deployment = vehicleContentTitleName.getDeployment();
			deployment = deployment != null ? deployment : new ArrayList<>(); 
			
			List<DeploymentContentCarName> carName = vehicleContentTitleName.getCarName();
			carName = carName != null ? carName : new ArrayList<>();
			
			List<VehicleContentTownName> townName = vehicleContentTitleName.getTownName();
			townName = townName != null ? townName : new ArrayList<>();
			
			List<VehicleContentDisasterType> disasterType = vehicleContentTitleName.getDisasterType();
			disasterType = disasterType != null ? disasterType : new ArrayList<>();
			
			List<VehicleContentAvmDynamicState> avmDynamicState = vehicleContentTitleName.getAvmDynamicState();
			avmDynamicState = avmDynamicState != null ? avmDynamicState : new ArrayList<>();
			
			List<BlinkSetting> lightingSetting = vehicleContentTitleName.getLightingSetting();
			lightingSetting = lightingSetting != null ? lightingSetting : new ArrayList<>();

			int[] sizes = {
					deployment.size(),
					carName.size(),
					townName.size(),
					disasterType.size(),
					avmDynamicState.size(),
					lightingSetting.size()
			};

			int size = Arrays.stream(sizes).max().orElse(0);

			List<CustomVehicleContentCarEntity> list = IntStream.range(0, size).mapToObj(i -> new CustomVehicleContentCarEntity()).collect(Collectors.toList());

			for (int idx = 0; idx < deployment.size(); idx++) {

				VehicleContentDeployment element = deployment.get(idx);

				String displayText = element.getDisplayText();
				String textColor = element.getTextColor();
				String backgroundColor = element.getBackgroundColor();

				CustomVehicleContentCarEntity entity = list.get(idx);

				entity.setDeployment(new DisplayItem(displayText, textColor, backgroundColor));
			}

			for (int idx = 0; idx < carName.size(); idx++) {

				DeploymentContentCarName element = carName.get(idx);

				String displayText = element.getDisplayText();
				String textColor = element.getTextColor();
				String backgroundColor = element.getBackgroundColor();

				CustomVehicleContentCarEntity entity = list.get(idx);

				entity.setCarName(new DisplayItem(displayText, textColor, backgroundColor));
			}

			for (int idx = 0; idx < townName.size(); idx++) {

				VehicleContentTownName element = townName.get(idx);

				String displayText = element.getDisplayText();
				String textColor = element.getTextColor();
				String backgroundColor = element.getBackgroundColor();

				CustomVehicleContentCarEntity entity = list.get(idx);

				entity.setTownName(new DisplayItem(displayText, textColor, backgroundColor));
			}

			for (int idx = 0; idx < disasterType.size(); idx++) {

				VehicleContentDisasterType element = disasterType.get(idx);

				String displayText = element.getDisplayText();
				String textColor = element.getTextColor();
				String backgroundColor = element.getBackgroundColor();

				CustomVehicleContentCarEntity entity = list.get(idx);

				entity.setDisasterType(new DisplayItem(displayText, textColor, backgroundColor));
			}

			for (int idx = 0; idx < avmDynamicState.size(); idx++) {

				VehicleContentAvmDynamicState element = avmDynamicState.get(idx);

				String displayText = element.getDisplayText();
				String textColor = element.getTextColor();
				String backgroundColor = element.getBackgroundColor();

				CustomVehicleContentCarEntity entity = list.get(idx);

				entity.setAvmDynamicState(new DisplayItem(displayText, textColor, backgroundColor));
			}

			for (int idx = 0; idx < lightingSetting.size(); idx++) {

				BlinkSetting element = lightingSetting.get(idx);

				Integer blinkTime = element.getBlinkTime();
				String lightingTextColor = element.getLightingTextColor();
				String lightingBackgroundColor = element.getLightingBackgroundColor();
				String lightingStatus = element.getLightingStatus();
				Integer blinkSpeed = element.getBlinkSpeed();

				CustomVehicleContentCarEntity entity = list.get(idx);

				entity.setLightingSetting(new DisplayEffect(blinkTime, lightingTextColor, lightingBackgroundColor, lightingStatus, blinkSpeed));
			}

			records.addAll(list);
		}
		
		int fromIndex = ((groupId - 1) * 8);
		int toIndex = (((groupId - 1) * 8) + 8);

		List<CustomVehicleContentEntity> result = IntStream.range(fromIndex, Math.min(toIndex, records.size()))
				.mapToObj(records::get)
				.collect(Collectors.toList());

		if (result != null && result.iterator().hasNext())
			this.items.addAll(result);
	}

	@JsonProperty("group_id")
	private int groupId;

	@JsonProperty("is_deployment")
	private Integer isDeployment = null;

	@JsonProperty("items")
	private List<CustomVehicleContentEntity> items;
	
	@JsonProperty("column_position")
	private String columnPosition;

}
